<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { authStore } from '$lib/stores/auth';

  onMount(() => {
    // Redirect based on authentication status
    const unsubscribe = authStore.subscribe((auth) => {
      if (auth.isAuthenticated && auth.user) {
        // User is authenticated, redirect to their portal
        switch (auth.user.role) {
          case 'ADMIN':
            goto('/admin/dashboard');
            break;
          case 'SARAF':
            goto('/saraf/dashboard');
            break;
          case 'CUSTOMER':
            goto('/customer/dashboard');
            break;
          default:
            goto('/login');
        }
      } else if (!auth.isLoading) {
        // User is not authenticated, redirect to login
        goto('/login');
      }
    });

    return unsubscribe;
  });
</script>

<!-- This page will redirect automatically -->
<div class="min-h-screen flex items-center justify-center">
  <div class="text-center">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
    <p class="text-gray-600">Redirecting...</p>
  </div>
</div>
