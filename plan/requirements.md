# HesabPlus - Comprehensive Financial Management System

## Project Vision & Principles

HesabPlus is a comprehensive financial management system designed to revolutionize how Sara<PERSON> (money exchangers) manage their operations, partnerships, and customer relationships. Built with Svelte<PERSON>it (TypeScript/JavaScript) frontend and Go Gin backend, the platform emphasizes security, scalability, and user experience while maintaining the traditional trust-based relationships that are fundamental to the Saraf business model.

### Core Principles
1. **Security First**: All financial data is encrypted and protected
2. **Trust & Transparency**: Maintaining traditional Saraf values in digital form
3. **Scalability**: Supporting growth from individual Sarafs to large networks
4. **User Experience**: Intuitive interfaces for all user types
5. **Compliance**: Adhering to financial regulations and best practices

## Architecture Overview

The system consists of three main portals, each tailored to specific user needs:

### 1. Admin Portal
- **Technology**: SvelteKit (TypeScript/JavaScript) frontend, Go Gin backend
- **Purpose**: SaaS platform management and system administration
- **Users**: Platform owners and system administrators
- **Key Features**: Platform management, subscription management, Saraf onboarding approval (leverages Coolify for system monitoring and analytics)

### 2. Saraf App
- **Technology**: SvelteKit (TypeScript/JavaScript) frontend, Go Gin backend
- **Purpose**: Core business operations for money exchangers
- **Users**: Sarafs (money exchangers)
- **Key Features**: Ledger management, transaction processing, partnership management

### 3. Customer Portal
- **Technology**: SvelteKit (TypeScript/JavaScript) frontend, Go Gin backend
- **Purpose**: Customer account access and transaction history
- **Users**: Saraf customers
- **Key Features**: Account viewing, transaction history, balance inquiries

## Feature Matrix

| Feature Category | Admin Portal | Saraf App | Customer Portal |
|------------------|--------------|-----------|------------------|
| Platform Management | ✅ Full | ❌ None | ❌ None |
| Saraf Registration | ✅ Approve/Reject | ❌ None | ❌ None |
| Subscription Management | ✅ Full | ❌ None | ❌ None |
| Platform Analytics | ✅ Via Coolify | ❌ None | ❌ None |
| Ledger Management | ❌ No Access | ✅ Own Only | ❌ None |
| Transaction Processing | ❌ No Access | ✅ Full | ❌ None |
| Partnership Management | ❌ No Access | ✅ Full | ❌ None |
| Hawala System | ❌ No Access | ✅ Full | ❌ None |
| Reporting | ✅ Platform Analytics | ✅ Own Data | ✅ Personal |
| Customer Management | ❌ No Access | ✅ Own Customers | ❌ None |
| Account Viewing | ❌ No Access | ✅ Own Accounts | ✅ Own Account |

## Detailed Feature Requirements

### Admin Portal Features

#### Platform Management
- **Saraf Onboarding**: Approve/reject new Saraf registrations
- **Subscription Management**: Manage Saraf subscription plans and billing
- **Platform Configuration**: Manage system-wide settings and features
- **Audit Trail**: Track platform-level administrative actions

#### Business Analytics
- **Saraf Metrics**: Number of active Sarafs, subscription status
- **Platform Usage**: Feature adoption and usage patterns
- **Revenue Analytics**: Subscription revenue and growth metrics
- **Support Metrics**: Support ticket volume and resolution times

#### Platform Operations
- **Support Management**: Handle platform-level support requests
- **Compliance Oversight**: Ensure platform regulatory compliance
- **Feature Management**: Enable/disable platform features
- **Communication**: Send platform-wide announcements to Sarafs

**Note**: System monitoring, backups, deployments, and infrastructure management are handled by Coolify hosting platform.

**Important**: Admin Portal has NO access to individual Saraf data, customer information, or transaction details to ensure privacy and data protection.

### Saraf App Features

#### Ledger Management
- **Digital Ledger**: Comprehensive transaction recording
- **Balance Tracking**: Real-time balance calculations
- **Transaction Categories**: Categorize transactions by type
- **Search & Filter**: Advanced transaction search capabilities
- **Export Options**: Export ledger data in various formats

#### Customer Management
- **Customer Profiles**: Detailed customer information
- **Account Creation**: Set up new customer accounts
- **Credit Limits**: Manage customer credit limits
- **Transaction History**: View customer transaction history
- **Communication Tools**: Send notifications to customers

#### Transaction Processing
- **Money Exchange**: Process currency exchange transactions
- **Transfers**: Handle money transfers between accounts
- **Deposits/Withdrawals**: Process cash transactions
- **Fee Calculation**: Automatic fee calculation and application
- **Receipt Generation**: Generate transaction receipts

#### Partnership Management
- **Partner Network**: Manage relationships with other Sarafs
- **Joint Transactions**: Process transactions involving partners
- **Settlement Management**: Handle inter-Saraf settlements
- **Commission Tracking**: Track partnership commissions

#### Hawala System
- **Hawala Transactions**: Process traditional Hawala transfers
- **Network Coordination**: Coordinate with Hawala network
- **Trust Management**: Manage trust relationships
- **Settlement Tracking**: Track Hawala settlements

#### Reporting & Analytics
- **Financial Reports**: Generate comprehensive financial reports
- **Performance Analytics**: Analyze business performance
- **Customer Analytics**: Understand customer behavior
- **Regulatory Reports**: Generate compliance reports

### Customer Portal Features

#### Account Access
- **Secure Login**: Multi-factor authentication
- **Account Overview**: View account summary and balance
- **Profile Management**: Update personal information

#### Transaction History
- **Transaction List**: View all personal transactions
- **Search & Filter**: Find specific transactions
- **Transaction Details**: View detailed transaction information
- **Download Statements**: Export transaction statements

#### Communication
- **Notifications**: Receive important updates
- **Message Center**: Communicate with Saraf
- **Support Requests**: Submit support tickets

## Registration Flow

### SaaS Hierarchy (No Public Registration)
1. **SaaS Owner** → Creates and approves **Saraf accounts**
2. **Saraf** → Creates **Customer accounts** for their clients
3. **Customer** → Receives login credentials from their Saraf

### Registration Process
- **No public registration allowed**
- **Saraf Registration**: Only through Admin Portal approval
- **Customer Registration**: Only through Saraf Portal by authenticated Sarafs
- All registrations require manual approval and verification

## Permissions & Data Boundaries

### Admin Portal Permissions
- **Platform Management Only**: Access limited to platform operations
- **Saraf Registration**: Approve/reject new Saraf applications
- **Subscription Control**: Manage billing and subscription plans
- **System Configuration**: Modify platform-wide settings only
- **NO DATA ACCESS**: Strictly prohibited from accessing Saraf business data, customer information, or transaction details
- **Business Analytics Only**: Access to business metrics via Coolify dashboard and custom analytics

### Saraf App Permissions
- **Own Data Only**: Access limited to own customers and transactions
- **Customer Management**: Full control over own customers
- **Transaction Processing**: Process transactions for own customers
- **Partner Interaction**: Limited access to partner data for joint transactions

### Customer Portal Permissions
- **Personal Data Only**: Access limited to own account information
- **Read-Only Access**: Cannot modify transaction data
- **Profile Updates**: Can update own contact information
- **Communication**: Can communicate with assigned Saraf

## Security & Compliance

### Data Encryption
- **At Rest**: All sensitive data encrypted using Go's crypto package
- **In Transit**: HTTPS/TLS encryption for all communications
- **Client-Side**: Sensitive data encrypted before transmission

### Authentication & Authorization
- **Multi-Factor Authentication**: Required for all users
- **Role-Based Access Control**: Granular permission system
- **Session Management**: Secure session handling with JWT
- **Password Policies**: Strong password requirements

### Audit & Compliance
- **Audit Logging**: Comprehensive logging of all actions
- **Data Retention**: Configurable data retention policies
- **Regulatory Compliance**: Adherence to financial regulations
- **Privacy Protection**: GDPR and privacy law compliance

## Performance & Scalability

### Performance Requirements
- **Response Time**: < 2 seconds for all operations
- **Concurrent Users**: Support 1000+ concurrent users
- **Data Processing**: Handle large transaction volumes
- **Real-time Updates**: Instant balance and status updates

### Scalability Design
- **Microservices Architecture**: Scalable Go Gin services
- **Goroutines**: Efficient concurrent request handling
- **Database Connection Pooling**: Optimized database connections
- **Database Optimization**: Efficient PostgreSQL queries
- **Caching Strategy**: Redis caching for performance
- **Load Balancing**: Horizontal scaling capabilities

### Technology Optimization
- **Node.js Event Loop**: Efficient asynchronous processing
- **Database Indexing**: Optimized database performance
- **CDN Integration**: Fast content delivery
- **Monitoring**: Real-time performance monitoring

## Tech Stack & Rationale

### Frontend
- **Framework**: SvelteKit (TypeScript/JavaScript)
- **Styling**: Tailwind CSS
- **State Management**: Svelte stores
- **Form Handling**: Svelte native forms with validation
- **UI Components**: Custom Svelte component library

### Backend
- **Runtime**: Node.js with TypeScript (e.g., NestJS, Express)
- **API Design**: RESTful APIs with OpenAPI documentation
- **Authentication**: JWT with refresh tokens
- **Validation**: Joi/Zod for request validation
- **File Upload**: Multer for file handling

### Database
- **Primary Database**: PostgreSQL
- **ORM**: Prisma/TypeORM
- **Caching**: Redis for session and data caching
- **Search**: PostgreSQL full-text search

### DevOps
- **Containerization**: Docker
- **Deployment**: Coolify
- **CI/CD**: GitHub Actions
- **Monitoring**: Application performance monitoring
- **Logging**: Structured logging with Winston

### Security
- **Encryption**: Node.js crypto for data encryption
- **HTTPS**: SSL/TLS certificates
- **Rate Limiting**: Express rate limiting
- **CORS**: Configured CORS policies

## Development Phases

### Phase 1: SaaS Foundation (Weeks 1-4)
- Set up development environment
- Implement authentication system (JWT, role-based)
- **Admin Portal**: SaaS owner dashboard
  - Saraf registration and approval system
  - Subscription management
  - Platform analytics
- Develop core database schema with multi-tenant isolation

### Phase 2: Saraf Core Features (Weeks 5-8)
- **Saraf Portal**: Money exchanger operations
  - Customer registration by Sarafs (no public signup)
  - Ledger management
  - Transaction processing
  - Customer management
- Build basic reporting and analytics

### Phase 3: Advanced Saraf Features (Weeks 9-12)
- Implement partnership management
- Develop Hawala system
- Create advanced reporting
- Add notification system
- Multi-tenant data isolation testing

### Phase 4: Customer Portal (Weeks 13-16)
- **Customer Portal**: Read-only access
  - Transaction history viewing
  - Account balance checking
  - Receipt downloads
- Customer authentication (credentials from Saraf)
- Add communication tools

### Phase 5: Admin Portal Enhancement (Weeks 17-20)
- Implement system monitoring
- Create compliance reporting
- Develop data management tools
- Add audit capabilities
- Platform-wide analytics

### Phase 6: Testing & Deployment (Weeks 21-24)
- Multi-Saraf testing
- Security auditing
- Performance optimization
- Production deployment

## Success Metrics

### Technical Metrics
- **System Uptime**: 99.9% availability
- **Response Time**: < 2 seconds average
- **Error Rate**: < 0.1% of transactions
- **Security Incidents**: Zero data breaches

### Business Metrics
- **User Adoption**: 80% of target Sarafs onboarded
- **Transaction Volume**: Handle 10,000+ daily transactions
- **Customer Satisfaction**: 90%+ satisfaction rating
- **Operational Efficiency**: 50% reduction in manual processes

## Appendix

### Glossary
- **Saraf**: Money exchanger or currency dealer
- **Hawala**: Traditional money transfer system
- **Ledger**: Record of financial transactions
- **Settlement**: Process of completing financial transactions

### References
- Financial industry best practices
- Security compliance standards
- User experience design principles
- Scalable architecture patterns

---

*This document serves as the comprehensive requirements specification for the HesabPlus financial management system. It should be regularly updated as the project evolves and new requirements are identified.*