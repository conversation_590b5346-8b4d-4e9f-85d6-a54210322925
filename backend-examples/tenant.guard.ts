import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserRole } from '../enums/user-role.enum';
import { TenantRequest } from '../interfaces/tenant-request.interface';

@Injectable()
export class TenantGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<TenantRequest>();
    const user = request.user;

    if (!user) {
      return false;
    }

    // Admin users have access to platform-level operations only
    if (user.role === UserRole.ADMIN) {
      return this.validateAdminAccess(request);
    }

    // Saraf users can only access their own tenant data
    if (user.role === UserRole.SARAF) {
      return this.validateSarafAccess(request, user);
    }

    // Customer users can only access their own data
    if (user.role === UserRole.CUSTOMER) {
      return this.validateCustomerAccess(request, user);
    }

    return false;
  }

  private validateAdminAccess(request: TenantRequest): boolean {
    // Admin should not access tenant-specific routes
    const isAdminRoute = request.url.startsWith('/admin');
    if (!isAdminRoute) {
      throw new ForbiddenException('Admins cannot access tenant-specific data');
    }
    return true;
  }

  private validateSarafAccess(request: TenantRequest, user: any): boolean {
    // Extract tenant ID from various sources
    const tenantId = this.extractTenantId(request);
    
    if (!tenantId) {
      return true; // No tenant context required
    }

    // Ensure Saraf can only access their own data
    if (user.sarafId !== tenantId) {
      throw new ForbiddenException('Access denied: Invalid tenant context');
    }

    // Set tenant context for downstream services
    request.tenantId = tenantId;
    return true;
  }

  private validateCustomerAccess(request: TenantRequest, user: any): boolean {
    // Customers can only access their own data
    const customerId = this.extractCustomerId(request);
    
    if (customerId && user.customerId !== customerId) {
      throw new ForbiddenException('Access denied: Invalid customer context');
    }

    // Set tenant context based on customer's Saraf
    request.tenantId = user.sarafId;
    return true;
  }

  private extractTenantId(request: TenantRequest): string | null {
    // Try to extract tenant ID from various sources
    return (
      request.params?.sarafId ||
      request.query?.sarafId ||
      request.body?.sarafId ||
      request.headers['x-tenant-id'] ||
      null
    );
  }

  private extractCustomerId(request: TenantRequest): string | null {
    return (
      request.params?.customerId ||
      request.query?.customerId ||
      request.body?.customerId ||
      null
    );
  }
}

// Tenant-aware request interface
export interface TenantRequest extends Request {
  user: {
    id: string;
    role: UserRole;
    sarafId?: string;
    customerId?: string;
  };
  tenantId?: string;
}
