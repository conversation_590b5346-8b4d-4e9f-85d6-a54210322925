// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum UserRole {
  ADMIN
  SARAF
  CUSTOMER
}

enum UserStatus {
  PENDING
  ACTIVE
  SUSPENDED
  INACTIVE
}

enum TransactionType {
  DEPOSIT
  WITHDRAWAL
  TRANSFER
  EXCHANGE
  HAWALA
  FEE
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
}

// Core User Management
model User {
  id        String     @id @default(cuid())
  email     String     @unique
  password  String
  role      UserRole
  status    UserStatus @default(PENDING)
  
  // Multi-language support
  preferredLanguage String @default("da") // Dari by default
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  lastLogin DateTime?
  
  // Relationships
  admin    Admin?
  saraf    <PERSON>?
  customer Customer?
  
  // Audit trail
  auditLogs AuditLog[]
  
  @@map("users")
}

// Admin Portal
model Admin {
  id     String @id @default(cuid())
  userId String @unique
  name   String
  
  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("admins")
}

// Saraf (Money Exchanger)
model Saraf {
  id           String @id @default(cuid())
  userId       String @unique
  businessName String
  licenseNumber String? @unique
  address      String?
  phone        String?
  
  // Business settings
  isActive     Boolean @default(false)
  subscription SubscriptionPlan @default(BASIC)
  
  // Relationships
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  customers    Customer[]
  transactions Transaction[]
  partnerships Partnership[] @relation("SarafPartnerships")
  partnerOf    Partnership[] @relation("PartnerSarafs")
  hawalaTransfers HawalaTransfer[]
  
  @@map("sarafs")
}

// Customer
model Customer {
  id       String @id @default(cuid())
  userId   String @unique
  sarafId  String // Tenant isolation key
  name     String
  phone    String?
  address  String?
  
  // Account information
  accountNumber String @unique
  balance       Decimal @default(0) @db.Decimal(15, 2)
  creditLimit   Decimal @default(0) @db.Decimal(15, 2)
  
  // Relationships
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  saraf        Saraf         @relation(fields: [sarafId], references: [id], onDelete: Cascade)
  transactions Transaction[]
  
  // Row Level Security for multi-tenancy
  @@index([sarafId])
  @@map("customers")
}

// Transactions
model Transaction {
  id          String            @id @default(cuid())
  sarafId     String            // Tenant isolation
  customerId  String?
  type        TransactionType
  status      TransactionStatus @default(PENDING)
  
  // Financial details
  amount      Decimal @db.Decimal(15, 2)
  fee         Decimal @default(0) @db.Decimal(15, 2)
  currency    String  @default("AFN") // Afghan Afghani
  exchangeRate Decimal? @db.Decimal(10, 4)
  
  // Transaction details
  description String?
  reference   String? @unique
  
  // Timestamps
  createdAt   DateTime @default(now())
  completedAt DateTime?
  
  // Relationships
  saraf    Saraf     @relation(fields: [sarafId], references: [id])
  customer Customer? @relation(fields: [customerId], references: [id])
  
  // Row Level Security
  @@index([sarafId])
  @@index([customerId])
  @@map("transactions")
}

// Partnership between Sarafs
model Partnership {
  id        String @id @default(cuid())
  sarafId   String
  partnerId String
  
  // Partnership details
  isActive      Boolean @default(true)
  commissionRate Decimal @default(0) @db.Decimal(5, 4)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relationships
  saraf   Saraf @relation("SarafPartnerships", fields: [sarafId], references: [id])
  partner Saraf @relation("PartnerSarafs", fields: [partnerId], references: [id])
  
  @@unique([sarafId, partnerId])
  @@map("partnerships")
}

// Hawala System
model HawalaTransfer {
  id           String            @id @default(cuid())
  sarafId      String
  senderName   String
  receiverName String
  amount       Decimal           @db.Decimal(15, 2)
  currency     String
  status       TransactionStatus @default(PENDING)
  
  // Hawala specific
  hawalaCode   String @unique
  destination  String
  
  // Timestamps
  createdAt    DateTime @default(now())
  completedAt  DateTime?
  
  // Relationships
  saraf Saraf @relation(fields: [sarafId], references: [id])
  
  @@index([sarafId])
  @@map("hawala_transfers")
}

// Subscription Management
enum SubscriptionPlan {
  BASIC
  PREMIUM
  ENTERPRISE
}

// Audit Logging
model AuditLog {
  id        String   @id @default(cuid())
  userId    String
  action    String
  entity    String
  entityId  String?
  oldValues Json?
  newValues Json?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())
  
  // Relationships
  user User @relation(fields: [userId], references: [id])
  
  @@index([userId])
  @@index([entity, entityId])
  @@map("audit_logs")
}
