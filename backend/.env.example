# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/hesabplus_db?schema=public"

# Authentication
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"
BETTER_AUTH_SECRET="your-better-auth-secret-key-change-this-in-production"
BETTER_AUTH_URL="http://localhost:3001"

# Application Configuration
NODE_ENV="development"
PORT=3001
API_PREFIX="api/v1"

# Redis Configuration (for caching and sessions)
REDIS_HOST="localhost"
REDIS_PORT=6379
REDIS_PASSWORD=""

# CORS Configuration
FRONTEND_URL="http://localhost:5173"
ALLOWED_ORIGINS="http://localhost:5173,http://localhost:3000"

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# File Upload
MAX_FILE_SIZE=5242880  # 5MB in bytes
UPLOAD_DEST="./uploads"

# Email Configuration (for notifications)
SMTP_HOST=""
SMTP_PORT=587
SMTP_USER=""
SMTP_PASS=""
FROM_EMAIL="<EMAIL>"

# Encryption
ENCRYPTION_KEY="your-32-character-encryption-key-here"

# Audit Logging
ENABLE_AUDIT_LOGS=true
AUDIT_LOG_RETENTION_DAYS=365

# Afghan Localization
DEFAULT_LANGUAGE="da"  # Dari
DEFAULT_CURRENCY="AFN"  # Afghan Afghani
DEFAULT_TIMEZONE="Asia/Kabul"

# Development Tools
ENABLE_SWAGGER=true
ENABLE_CORS=true
LOG_LEVEL="debug"
