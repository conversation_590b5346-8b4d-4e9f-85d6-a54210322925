{"name": "create-jest", "description": "Create a new Jest project", "version": "29.7.0", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/create-jest"}, "license": "MIT", "bin": "./bin/create-jest.js", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json", "./bin/create-jest": "./bin/create-jest.js"}, "dependencies": {"@jest/types": "^29.6.3", "chalk": "^4.0.0", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "jest-config": "^29.7.0", "jest-util": "^29.7.0", "prompts": "^2.0.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "publishConfig": {"access": "public"}, "devDependencies": {"@types/exit": "^0.1.30", "@types/graceful-fs": "^4.1.3", "@types/prompts": "^2.0.1"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630"}