"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketSubject = exports.webSocket = void 0;
var webSocket_1 = require("../internal/observable/dom/webSocket");
Object.defineProperty(exports, "webSocket", { enumerable: true, get: function () { return webSocket_1.webSocket; } });
var WebSocketSubject_1 = require("../internal/observable/dom/WebSocketSubject");
Object.defineProperty(exports, "WebSocketSubject", { enumerable: true, get: function () { return WebSocketSubject_1.WebSocketSubject; } });
//# sourceMappingURL=index.js.map